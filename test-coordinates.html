<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Coordinate Normalization Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
        }
        .test-container {
            border: 1px solid #ccc;
            padding: 20px;
            margin: 10px 0;
        }
        .canvas-container {
            border: 2px solid #333;
            margin: 10px 0;
        }
        button {
            margin: 5px;
            padding: 10px 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .info {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .log {
            background: #000;
            color: #0f0;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
            height: 200px;
            overflow-y: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Coordinate Normalization Test</h1>
    
    <div class="info">
        <p>This test verifies that objects appear at the same relative positions across different screen sizes.</p>
        <p>Instructions:</p>
        <ol>
            <li>Click "Add Test Object" to add an object at the center</li>
            <li>Click "Simulate Different Screen Size" to change canvas dimensions</li>
            <li>Observe that the object maintains its relative position</li>
        </ol>
    </div>

    <div class="test-container">
        <h3>Canvas 1 (Original Size)</h3>
        <div class="canvas-container">
            <canvas id="canvas1" width="800" height="600"></canvas>
        </div>
        <button onclick="addTestObject(canvas1, 'Canvas 1')">Add Test Object</button>
        <button onclick="testSerialization(canvas1, 'Canvas 1')">Test Serialization</button>
    </div>

    <div class="test-container">
        <h3>Canvas 2 (Different Size)</h3>
        <div class="canvas-container">
            <canvas id="canvas2" width="600" height="400"></canvas>
        </div>
        <button onclick="addTestObject(canvas2, 'Canvas 2')">Add Test Object</button>
        <button onclick="testSerialization(canvas2, 'Canvas 2')">Test Serialization</button>
    </div>

    <div class="test-container">
        <button onclick="testCrossCanvasSync()">Test Cross-Canvas Sync</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>

    <div class="log" id="log"></div>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/fabric.js/5.3.0/fabric.min.js"></script>
    <script>
        // Initialize canvases
        const canvas1 = new fabric.Canvas('canvas1');
        const canvas2 = new fabric.Canvas('canvas2');

        // Mock collaboration manager methods for testing
        function absoluteToNormalized(canvas, x, y) {
            const normalizedX = x / canvas.width;
            const normalizedY = y / canvas.height;
            return { x: normalizedX, y: normalizedY };
        }

        function normalizedToAbsolute(canvas, normalizedX, normalizedY) {
            const absoluteX = normalizedX * canvas.width;
            const absoluteY = normalizedY * canvas.height;
            return { x: absoluteX, y: absoluteY };
        }

        function log(message) {
            const logElement = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}\n`;
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }

        function addTestObject(canvas, canvasName) {
            // Add a test object at the center
            const centerX = canvas.width / 2;
            const centerY = canvas.height / 2;

            const circle = new fabric.Circle({
                left: centerX,
                top: centerY,
                radius: 30,
                fill: 'red',
                originX: 'center',
                originY: 'center'
            });

            canvas.add(circle);
            log(`Added test object to ${canvasName} at (${centerX}, ${centerY})`);
        }

        function testSerialization(canvas, canvasName) {
            const objects = canvas.getObjects();
            if (objects.length === 0) {
                log(`No objects to test in ${canvasName}`);
                return;
            }

            const obj = objects[0];
            const normalized = absoluteToNormalized(canvas, obj.left, obj.top);
            const backToAbsolute = normalizedToAbsolute(canvas, normalized.x, normalized.y);

            log(`${canvasName} - Original: (${obj.left}, ${obj.top})`);
            log(`${canvasName} - Normalized: (${normalized.x.toFixed(3)}, ${normalized.y.toFixed(3)})`);
            log(`${canvasName} - Back to absolute: (${backToAbsolute.x}, ${backToAbsolute.y})`);
        }

        function testCrossCanvasSync() {
            const objects1 = canvas1.getObjects();
            const objects2 = canvas2.getObjects();

            if (objects1.length === 0) {
                log('No objects in Canvas 1 to sync');
                return;
            }

            // Clear canvas 2
            canvas2.clear();

            // Simulate sending object from canvas1 to canvas2
            const obj1 = objects1[0];
            const normalized = absoluteToNormalized(canvas1, obj1.left, obj1.top);
            const newPosition = normalizedToAbsolute(canvas2, normalized.x, normalized.y);

            const syncedCircle = new fabric.Circle({
                left: newPosition.x,
                top: newPosition.y,
                radius: obj1.radius,
                fill: 'blue', // Different color to show it's synced
                originX: 'center',
                originY: 'center'
            });

            canvas2.add(syncedCircle);

            log(`Synced object from Canvas 1 to Canvas 2:`);
            log(`  Canvas 1 position: (${obj1.left}, ${obj1.top})`);
            log(`  Normalized: (${normalized.x.toFixed(3)}, ${normalized.y.toFixed(3)})`);
            log(`  Canvas 2 position: (${newPosition.x}, ${newPosition.y})`);
            log(`  Relative position should be the same!`);
        }

        // Initial log
        log('Coordinate normalization test initialized');
        log(`Canvas 1 size: ${canvas1.width}x${canvas1.height}`);
        log(`Canvas 2 size: ${canvas2.width}x${canvas2.height}`);
    </script>
</body>
</html>
